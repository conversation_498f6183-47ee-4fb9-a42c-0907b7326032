using Microsoft.Office.Core;
using Microsoft.Office.Interop.Outlook;
using Microsoft.Office.Tools.Ribbon;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using Exception = System.Exception;
using Outlook = Microsoft.Office.Interop.Outlook;

namespace webhook
{
    public partial class Ribbon1
    {
        private void Ribbon1_Load(object sender, RibbonUIEventArgs e)
        {
          
        }

        private async void button1_ClickAsync(object sender, RibbonControlEventArgs e)
        {   var app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;
            DialogResult result = DialogResult.No;
            if (selection.Count > 0 && selection[1] is MailItem mail1)
            {
                string msg = string.Format("发件人：{0}\n邮件标题：{1}", mail1.Sender?.Address, mail1.Subject);

              result  = MessageBox.Show( msg,"保存新记录到数据库",
                         MessageBoxButtons.YesNo,
                         MessageBoxIcon.Warning
                           );
            }

            if (result == DialogResult.Yes)
            {
                try
                {


                    string pic = await Helper.ConvertSelectedMailToPngBase64Async();
                    if (selection.Count > 0 && selection[1] is MailItem mail)
                    {
                        // 原有的JSON数据发送
                        var jsonData = new
                        {
                            发件人 = mail.Sender?.Address,
                            主题 = mail.Subject,
                            content = Helper.FormatOutlookEmail(mail.Body),
                            picture = pic,
                            company = eb_company.Text.Trim(),
                            from = eb_from.Text.Trim(),
                            to=eb_to.Text.Trim(),
                            vol=eb_vol.Text.Trim()

                        };

                        var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);

                       

                        using (var client = new HttpClient())
                        {
                            client.DefaultRequestHeaders.Add("token", "sdyfangye");
                            client.DefaultRequestHeaders.Add("function", "create_row");

                            var content = new StringContent(json, Encoding.UTF8, "application/json");
                            var response = client.PostAsync(Helper.WebHook_base + Helper.WebHook_func.create_row, content).Result;//"http://sdy.cpolar.top/webhook-test/create_row", content).Result;
                            string responseBody = await response.Content.ReadAsStringAsync();

                            var obj = JObject.Parse(responseBody);
                            string message = obj["message"]?.ToString();

                            if (response.IsSuccessStatusCode)
                            {
                                MessageBox.Show(message);

                                // 在JSON发送成功后，再发送MSG文件
                                //    await SaveAndSendMsgFileAsync(mail);
                            }
                            else
                                MessageBox.Show("保存失败：" + response.StatusCode);
                        }
                    }
                    else
                    {
                        MessageBox.Show("请选择一封邮件！");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("错误: " + ex.Message);
                }
            }


           
        }

        // 新增方法：保存邮件为MSG文件并发送到Webhook
        private async Task SaveAndSendMsgFileAsync(MailItem mail)
        {
            // 创建临时文件路径
            string tempPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.msg");
            
            try
            {
                // 保存为MSG文件
                mail.SaveAs(tempPath, Outlook.OlSaveAsType.olMSG);
                
                // 读取文件并转换为Base64
                byte[] fileBytes = File.ReadAllBytes(tempPath);
                string base64Content = Convert.ToBase64String(fileBytes);
                
                // 创建包含Base64数据的JSON对象
                var jsonData = new
                {
                    subject = mail.Subject ?? "",
                    sender = mail.Sender?.Address ?? "",
                    fileName = Path.GetFileName(tempPath),
                    fileContent = base64Content,
                    fileType = "msg"
                };

                var json = JsonConvert.SerializeObject(jsonData);

                // 发送Base64编码的数据
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("token", "sdyfangye");
                    client.DefaultRequestHeaders.Add("function", "create_row");
                    
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("http://sdy.cpolar.top/webhook/mail-msg", content);
                    string responseBody = await response.Content.ReadAsStringAsync();
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var obj = JObject.Parse(responseBody);
                        string message = obj["message"]?.ToString() ?? "MSG文件已成功发送到Webhook！";
                        MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show($"MSG文件发送失败: {response.StatusCode}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"MSG文件发送过程中出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 删除临时文件
                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }
            }
        }


        private async void button2_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var app = Globals.ThisAddIn.Application;
                var selection = app.ActiveExplorer().Selection;

                if (selection.Count > 0 && selection[1] is MailItem mail)
                {
                    // 原有的JSON数据发送
                    var jsonData = new
                    {
                        发件人 = mail.Sender?.Address,
                        主题 = mail.Subject,
                        content = Helper.FormatOutlookEmail(mail.Body)
                    };

                    var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);

                    using (var client = new HttpClient())
                    {
                        client.DefaultRequestHeaders.Add("token", "sdyfangye");
                        client.DefaultRequestHeaders.Add("function", "create_row");

                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                       

                       
                       

                      

                            // 在JSON发送成功后，再发送MSG文件
                            await SaveAndSendMsgFileAsync(mail);
                      
                    }
                }
                else
                {
                    MessageBox.Show("请选择一封邮件！");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("错误: " + ex.Message);
            }
        }

        //保存联系人信息。
        private async void button3_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var app = Globals.ThisAddIn.Application;
                var selection = app.ActiveExplorer().Selection;

                if (selection.Count > 0 && selection[1] is MailItem mail)
                {
                    // 原有的JSON数据发送
                    var jsonData = new
                    {
                        发件人 = mail.Sender?.Address,
                        主题 = mail.Subject,
                        content = Helper.FormatOutlookEmail(mail.Body)
                    };

                    var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);

                    using (var client = new HttpClient())
                    {
                        client.DefaultRequestHeaders.Add("token", "sdyfangye");
                        client.DefaultRequestHeaders.Add("function", "savecompany");

                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var response = client.PostAsync(Helper.WebHook_base+Helper.WebHook_func.savecompany, content).Result;
                        string responseBody = await response.Content.ReadAsStringAsync();

                        var obj = JObject.Parse(responseBody);
                        string message = obj["message"]?.ToString();

                        if (response.IsSuccessStatusCode)
                        {
                            MessageBox.Show(message);

                            // 在JSON发送成功后，再发送MSG文件
                            //    await SaveAndSendMsgFileAsync(mail);
                        }
                        else
                            MessageBox.Show("保存失败：" + response.StatusCode);
                    }
                }
                else
                {
                    MessageBox.Show("请选择一封邮件！");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("错误: " + ex.Message);
            }
        }

        private async void button4_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;
            DialogResult result = DialogResult.No;
            if (selection.Count > 0 && selection[1] is MailItem mail1)
            {
                Match match = Regex.Match(mail1.Subject, @"G(R)?\d{6}WN\d{1,2}");

                //   string msg = string.Format("内部参考号:{0}\n发件人：{1}\n邮件标题：{2}",match.Value ,Helper.GetFirstRecipientEmail(mail1), mail1.Subject);

                //result = MessageBox.Show(msg, "保存新记录到数据库",
                //           MessageBoxButtons.YesNo,
                //           MessageBoxIcon.Warning
                //             );

                result = Helper.ShowCustomMessageBox(match.Value, Helper.GetFirstRecipientEmail(mail1), mail1.Subject,match.Value+"被更新到对应记录！");

            }

            if (result == DialogResult.Yes)
            {


                try
                {
                    //var app = Globals.ThisAddIn.Application;
                    //var selection = app.ActiveExplorer().Selection;

                     string pic = await Helper.ConvertSelectedMailToPngBase64Async();
                    if (selection.Count > 0 && selection[1] is MailItem mail)
                    {


                        // 原有的JSON数据发送
                        var jsonData = new
                        {
                            发件人 = mail.Sender?.Address,
                            主题 = mail.Subject,
                            收件人 = Helper.GetFirstRecipientEmail(mail),
                          //  content = Helper.FormatOutlookEmail(mail.Body),
                            picture=pic
                        };

                        var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);

                        using (var client = new HttpClient())
                        {
                            client.DefaultRequestHeaders.Add("token", "sdyfangye");
                            client.DefaultRequestHeaders.Add("function", "create_row");

                            var content = new StringContent(json, Encoding.UTF8, "application/json");
                            var response = client.PostAsync(Helper.WebHook_base + Helper.WebHook_func.update_row, content).Result;
                            string responseBody = await response.Content.ReadAsStringAsync();

                            var obj = JObject.Parse(responseBody);
                            string message = obj["message"]?.ToString();

                            if (response.IsSuccessStatusCode)
                            {
                                MessageBox.Show(message);

                                // 在JSON发送成功后，再发送MSG文件
                                //    await SaveAndSendMsgFileAsync(mail);
                            }
                            else
                                MessageBox.Show("保存失败：" + response.StatusCode);
                        }
                    }
                    else
                    {
                        MessageBox.Show("请选择一封邮件！");
                    }
                }
                catch (System.Exception ex)
                {
                    MessageBox.Show("错误: " + ex.Message);
                }

            }

        }
      
        private void button5_Click(object sender, RibbonControlEventArgs e)
        {
            //  string path =await Helper.ConvertSelectedMailToPngWithHeaderAsync();
           new Form1().Show();
         
        }

        private async void button2_Click_1(object sender, RibbonControlEventArgs e)
        {
            if (eb_cbj.Text.Trim() == "")
            {
                MessageBox.Show("请输入成本价！");
                return;
            }
            var app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;
           // DialogResult result = DialogResult.No;
            if (selection.Count > 0 && selection[1] is MailItem mail1)
            {
                Match match = Regex.Match(mail1.Subject, @"G(R)?\d{6}WN\d{1,2}");

                DialogResult result = MessageBox.Show(
     "参考号：" + match.Value + "\r\n" + "   价格：" + eb_cbj.Text.Trim(),     // 内容
      "更新成本价到NOCODB",                    // 标题
      MessageBoxButtons.YesNo,       // 按钮类型：是/否
      MessageBoxIcon.Exclamation        // 图标：问号
  );
                if (result == DialogResult.Yes)
                {

                    #region 发送数据到webhook

                    try
                    {
                        //var app = Globals.ThisAddIn.Application;
                        //var selection = app.ActiveExplorer().Selection;

                        string pic = await Helper.ConvertSelectedMailToPngBase64Async();
                        if (selection.Count > 0 && selection[1] is MailItem mail)
                        {


                            // 原有的JSON数据发送
                            var jsonData = new
                            {
                                参考号 = match.Value,
                                价格=eb_cbj.Text.Trim(),
                               
                            };

                            var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);

                            using (var client = new HttpClient())
                            {
                                client.DefaultRequestHeaders.Add("token", "sdyfangye");
                                client.DefaultRequestHeaders.Add("function", "up_prive");

                                var content = new StringContent(json, Encoding.UTF8, "application/json");
                                var response = client.PostAsync(Helper.WebHook_base + Helper.WebHook_func.up_price, content).Result;
                                string responseBody = await response.Content.ReadAsStringAsync();

                                var obj = JObject.Parse(responseBody);
                                string message = obj["message"]?.ToString();

                                if (response.IsSuccessStatusCode)
                                {
                                    MessageBox.Show(message);

                                    // 在JSON发送成功后，再发送MSG文件
                                    //    await SaveAndSendMsgFileAsync(mail);
                                }
                                else
                                    MessageBox.Show("更新失败：" + response.StatusCode);
                            }
                        }
                        else
                        {
                            MessageBox.Show("请选择一封邮件！");
                        }
                    }
                    catch (System.Exception ex)
                    {
                        MessageBox.Show("错误: " + ex.Message);
                    }


                    #endregion



                }




            }
            else
            {
                MessageBox.Show("请选择一封邮件！需要带更新参考号的邮件。");
            }
        }
    }
}
