<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Ribbon1.button1_ClickAsync#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_ClickAsync</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-22T22:40:32.5892949+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.SaveAndSendMsgFileAsync#Task#MailItem</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SaveAndSendMsgFileAsync</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-22T22:39:45.3657683+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button3_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button3_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-22T17:18:45.2459305+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button2_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button2_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-22T00:15:05.2266174+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button1_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-20T00:21:25.2707258+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.FormatOutlookEmailstatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FormatOutlookEmail</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-20T00:20:46.6473699+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.LoadImage</ID><ImageSource>img\tvi\x_event-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadImage</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-20T00:07:28.0289113+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.Close</ID><ImageSource>img\tvi\x_event-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Close</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-19T23:52:41.3568869+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Ribbon1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Ribbon1.button4_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button4_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-24T22:10:37.0897656+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.GetOutlookMainWindowPosition#Point#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetOutlookMainWindowPosition</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-24T22:10:14.1983192+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.create_row#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>create_row</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-24T17:59:34.1809892+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button1_ClickAsync#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_ClickAsync</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-24T17:35:55.6120494+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Ribbon1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.ThisAddIn.FinishInitialization#void#</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FinishInitialization</ItemName><ItemPath>webhook.ThisAddIn</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.Designer.cs</ProjectItemFileName><TimeStamp>2025-05-19T23:39:49.8779862+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.ThisAddIn.ThisAddIn_Startup#void#object, System.EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ThisAddIn_Startup</ItemName><ItemPath>webhook.ThisAddIn</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-05-20T16:54:14.4200654+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.ThisAddIn.InternalStartup#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InternalStartup</ItemName><ItemPath>webhook.ThisAddIn</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-05-20T00:08:23.0467197+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.ThisAddIn.CreateRibbonExtensibilityObject#Microsoft.Office.Core.IRibbonExtensibility#</ID><ImageSource>img\tvi\x_method-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CreateRibbonExtensibilityObject</ItemName><ItemPath>webhook.ThisAddIn</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-05-19T23:31:21.36066+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.ThisAddIn</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ThisAddIn</ItemName><ItemPath>webhook</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.cs</ProjectItemFileName><TimeStamp>2025-05-19T23:13:43.4658623+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\ThisAddIn.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.NocoDBClient</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>NocoDBClient</ItemName><ItemPath>webhook</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName><TimeStamp>2025-05-25T19:34:03.1104093+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.NocoDBClient.CreateTableRecord#Task&lt;string&gt;#object</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateTableRecord</ItemName><ItemPath>webhook.NocoDBClient</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName><TimeStamp>2025-05-25T19:25:44.2563644+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.NocoDBClient.UpdateTableRecord#Task&lt;string&gt;#string, object</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UpdateTableRecord</ItemName><ItemPath>webhook.NocoDBClient</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName><TimeStamp>2025-05-25T19:25:02.578353+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.NocoDBClient.UploadFile#Task&lt;Newtonsoft.Json.Linq.JObject&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>UploadFile</ItemName><ItemPath>webhook.NocoDBClient</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName><TimeStamp>2025-05-25T19:21:02.1641075+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.NocoDBClient.DeleteTableRecord#Task&lt;string&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DeleteTableRecord</ItemName><ItemPath>webhook.NocoDBClient</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName><TimeStamp>2025-05-25T19:18:21.3841464+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.NocoDBClient.ListTableRecords#Task&lt;string&gt;#int, int</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ListTableRecords</ItemName><ItemPath>webhook.NocoDBClient</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName><TimeStamp>2025-05-25T19:11:11.3131032+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\NocoDBClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Form1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Form1.FormatOutlookEmailstatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FormatOutlookEmail</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-28T09:16:11.6692086+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Form1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Ribbon1.button1_ClickAsync#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_ClickAsync</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-28T09:16:26.0956305+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button4_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button4_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-26T21:51:18.6252012+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.Ribbon1_Load#void#object, RibbonUIEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Ribbon1_Load</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-26T21:42:51.6421926+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Ribbon1</ItemName><ItemPath>webhook</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-26T21:40:27.5429134+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button3_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button3_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-26T17:16:18.0430764+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button5_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button5_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-26T12:03:43.1327415+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Ribbon1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Helper.WebHook_func.savecompany</ID><ImageSource>img\tvi\x_var-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>savecompany</ItemName><ItemPath>webhook.Helper.WebHook_func</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-25T23:15:10.781831+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.WebHook_func.upload_file</ID><ImageSource>img\tvi\x_var-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>upload_file</ItemName><ItemPath>webhook.Helper.WebHook_func</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-25T23:15:08.5772816+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.ShowCustomMessageBoxstatic##DialogResult#string, string, string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ShowCustomMessageBox</ItemName><ItemPath>webhook.Helper</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-25T22:04:32.0498536+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.GetEmailAddressesstatic##List&lt;string&gt;#Outlook.MailItem</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetEmailAddresses</ItemName><ItemPath>webhook.Helper</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-25T13:43:16.7980808+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Helper</ItemName><ItemPath>webhook</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-24T22:58:36.0963661+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.WebHook_func</ID><ImageSource>img\tvi\y_enum-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebHook_func</ItemName><ItemPath>webhook.Helper</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-24T22:58:32.8426297+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.WebHook_func.Warning</ID><ImageSource>img\tvi\x_enumitem-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Warning</ItemName><ItemPath>webhook.Helper.WebHook_func</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-24T22:39:06.1261658+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.WebHook_func.upload_</ID><ImageSource>img\tvi\x_enumitem-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>upload_</ItemName><ItemPath>webhook.Helper.WebHook_func</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-24T22:39:03.078495+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Helper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Helper.ConvertSelectedMailToPngBase64Asyncstatic##Task&lt;string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertSelectedMailToPngBase64Async</ItemName><ItemPath>webhook.Helper</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-26T12:49:30.8979732+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.ConvertSelectedMailToPngWithHeaderAsyncstatic##Task&lt;string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertSelectedMailToPngWithHeaderAsync</ItemName><ItemPath>webhook.Helper</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-26T12:10:50.3746688+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Helper.ConvertSelectedMailToPngWithHeaderstatic##string#</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConvertSelectedMailToPngWithHeader</ItemName><ItemPath>webhook.Helper</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Helper.cs</ProjectItemFileName><TimeStamp>2025-05-26T12:00:27.0494164+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(邮件截图）\Helper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Ribbon1.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.Designer.cs</ProjectItemFileName><TimeStamp>2025-05-20T15:53:39.405431+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Ribbon1</ItemName><ItemPath>webhook</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.Designer.cs</ProjectItemFileName><TimeStamp>2025-05-20T00:07:27.7708954+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook\Ribbon1.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Form1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Form1.FormatOutlookEmailstatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FormatOutlookEmail</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-25T20:48:41.0521989+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Form1.button1_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_Click</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-25T20:47:57.4652066+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Form1.Form1##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Form1</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-25T14:53:28.3997424+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Form1.GetOutlookMainWindowPosition#Point#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetOutlookMainWindowPosition</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-24T23:14:35.6993827+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Form1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Form1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Form1.GetExplorerWindowPositionstatic##Point#Outlook.Application</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetExplorerWindowPosition</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-24T21:45:50.0377527+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Form1.button1_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_Click</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-24T21:36:08.6335484+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Form1.FormatOutlookEmailstatic##string#string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>FormatOutlookEmail</ItemName><ItemPath>webhook.Form1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Form1.cs</ProjectItemFileName><TimeStamp>2025-05-24T18:04:31.3195206+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(修改为每个功能对应一个webhook）\Form1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Ribbon1.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>webhook.Ribbon1.button3_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button3_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-25T23:15:33.6116218+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button4_Click#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button4_Click</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-25T21:39:46.1534226+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>webhook.Ribbon1.button1_ClickAsync#void#object, RibbonControlEventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button1_ClickAsync</ItemName><ItemPath>webhook.Ribbon1</ItemPath><ProjectFullName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\webhook.csproj</ProjectFullName><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Ribbon1.cs</ProjectItemFileName><TimeStamp>2025-05-25T20:51:28.6350037+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\软件DIY\outlook 插件 webhook发送到n8n\webhook(winform）\Ribbon1.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>webhook</ProjectName></ProjectData>