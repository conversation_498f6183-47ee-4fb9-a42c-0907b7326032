﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Outlook = Microsoft.Office.Interop.Outlook;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Drawing;
using System.IO;
using System.Drawing.Imaging;
using System.Threading;

namespace webhook
{
    public class Helper
    {
        #region 格式化Outlook邮件内容
        public static string FormatOutlookEmail(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return "";

            // 1. 替换 "\r\n" 为真正的换行符
            string output = input.Replace("\\r\\n", "\n");

            // 2. 移除 HTML 链接的格式 <https://...>
            output = Regex.Replace(output, @"<https?:\/\/[^>]+>", "");

            // 3. 移除 mailto 链接格式 <mailto:<EMAIL>>
            output = Regex.Replace(output, @"<mailto:[^>]+>", "");

            // 4. 移除冗余空行（超过2行的压缩为1行）
            output = Regex.Replace(output, @"\n{3,}", "\n\n");

            // 5. 修复一些邮件中常见问题，如： "cc" 独立一行
            output = Regex.Replace(output, @"(?<=\n)cc", "CC");

            // 6. 清除行首多余空格
            output = Regex.Replace(output, @"(?m)^[ \t]+", "");

            return output.Trim();
        }

        #endregion

        #region WebHook相关配置
        public static string WebHook_base = "http://sdy.cpolar.cn/webhook/";
        public static string WebHook_base_cloud = "https://n8n-icbxfmpa.ap-northeast-1.clawcloudrun.com/webhook-test/";
        public enum WebHook_func
        {
            create_row,
            update_row,
            up_price,
            upload_file,
            get_sheet_info,
            savecompany

        }

        #endregion
        public static List<string> GetEmailAddresses(Outlook.MailItem mail)
        {
            List<string> emailList = new List<string>();

            foreach (Outlook.Recipient recipient in mail.Recipients)
            {
                try
                {
                    Outlook.AddressEntry addressEntry = recipient.AddressEntry;
                    string email = recipient.Name; // 默认用显示名

                    if (addressEntry != null)
                    {
                        if (addressEntry.AddressEntryUserType ==
                            Outlook.OlAddressEntryUserType.olExchangeUserAddressEntry ||
                            addressEntry.AddressEntryUserType ==
                            Outlook.OlAddressEntryUserType.olExchangeRemoteUserAddressEntry)
                        {
                            Outlook.ExchangeUser exchUser = addressEntry.GetExchangeUser();
                            if (exchUser != null && !string.IsNullOrEmpty(exchUser.PrimarySmtpAddress))
                            {
                                email = exchUser.PrimarySmtpAddress;
                            }
                        }
                        else
                        {
                            // 其他类型直接取 Address
                            if (!string.IsNullOrEmpty(addressEntry.Address))
                                email = addressEntry.Address;
                        }
                    }

                    emailList.Add(email);
                }
                catch
                {
                    // 捕获异常防止个别解析失败
                    emailList.Add(recipient.Name);
                }
            }

            return emailList;
        }

        #region
        public static string Convvert_BH(DateTime date)
        {
            string day = date.Day.ToString("D2");      // 两位日期
            string month = date.Month.ToString("D2");  // 两位月份
            string year = (date.Year % 100).ToString("D2"); // 后两位年份

            return $"G{day}{month}{year}WN";
        }

        #endregion


        #region 获取第一个收件人邮箱
        public static string GetFirstRecipientEmail(Outlook.MailItem mail)
        {
            if (mail.Recipients.Count == 0)
                return string.Empty;

            try
            {
                var recipient = mail.Recipients[1]; // Outlook集合是从1开始的
                var entry = recipient.AddressEntry;

                if (entry != null)
                {
                    if (entry.AddressEntryUserType == Outlook.OlAddressEntryUserType.olExchangeUserAddressEntry ||
                        entry.AddressEntryUserType == Outlook.OlAddressEntryUserType.olExchangeRemoteUserAddressEntry)
                    {
                        var exchUser = entry.GetExchangeUser();
                        if (exchUser != null && !string.IsNullOrEmpty(exchUser.PrimarySmtpAddress))
                            return exchUser.PrimarySmtpAddress;
                    }

                    return entry.Address; // SMTP 地址或其他格式
                }

                return recipient.Name; // fallback
            }
            catch
            {
                return string.Empty;
            }
        }
        #endregion

        #region 验证邮箱格式
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            string pattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            return Regex.IsMatch(email, pattern, RegexOptions.IgnoreCase);
        }

        #endregion

        #region 自定义消息框
        public static DialogResult ShowCustomMessageBox(string reference, string senderEmail, string subject, string tishi)
        {
            Form form = new Form();
            form.Text = ""; // 移除系统标题文字
            form.Size = new Size(500, 320);
            form.StartPosition = FormStartPosition.CenterScreen;
            form.FormBorderStyle = FormBorderStyle.FixedDialog;
            form.MaximizeBox = false;
            form.MinimizeBox = false;
            form.ShowInTaskbar = false;

            // 添加自定义红色加粗标题
            Label titleLabel = new Label();
            titleLabel.Text = tishi;
            titleLabel.ForeColor = Color.Red;
            titleLabel.Font = new Font("黑体", 14, FontStyle.Bold);
            titleLabel.AutoSize = false;
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.Location = new Point(0, 0);
            titleLabel.Size = new Size(form.ClientSize.Width, 40);
            titleLabel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            form.Controls.Add(titleLabel);

            // 内容框
            RichTextBox richTextBox = new RichTextBox();
            richTextBox.ReadOnly = true;
            richTextBox.BorderStyle = BorderStyle.None;
            richTextBox.BackColor = form.BackColor;
            richTextBox.Location = new Point(20, 50);
            richTextBox.Size = new Size(440, 180);
            richTextBox.WordWrap = true;
            richTextBox.ScrollBars = RichTextBoxScrollBars.Vertical;

            richTextBox.Rtf = $@"{{\rtf1\ansi\deff0
{{\fonttbl
    {{\f0\fnil\fcharset134 宋体;}}
    {{\f1\fnil\fcharset134 黑体;}}
}}
{{\colortbl ;\red255\green0\blue0;}}
\b\f1 内部参考号：\b0\f0 {reference}\line
\b\f1 发件人：\b0\f0 {senderEmail}\line
\b\f1 邮件标题：\b0\f0 {subject}
}}";

            form.Controls.Add(richTextBox);

            // 是按钮
            Button btnYes = new Button();
            btnYes.Text = "是";
            btnYes.Location = new Point(150, 250);
            btnYes.DialogResult = DialogResult.Yes;
            form.Controls.Add(btnYes);

            // 否按钮
            Button btnNo = new Button();
            btnNo.Text = "否";
            btnNo.Location = new Point(260, 250);
            btnNo.DialogResult = DialogResult.No;
            form.Controls.Add(btnNo);

            form.AcceptButton = btnYes;
            form.CancelButton = btnNo;

            return form.ShowDialog();
        }



        //        public static DialogResult ShowCustomMessageBox(string reference, string senderEmail, string subject,string tishi)
        //        {
        //            Form form = new Form();
        //            form.Text = tishi;
        //            form.Size = new Size(500, 300);
        //            form.StartPosition = FormStartPosition.CenterScreen;
        //            form.FormBorderStyle = FormBorderStyle.FixedDialog;
        //            form.MaximizeBox = false;
        //            form.MinimizeBox = false;
        //            form.ShowInTaskbar = false;

        //            RichTextBox richTextBox = new RichTextBox();
        //            richTextBox.ReadOnly = true;
        //            richTextBox.BorderStyle = BorderStyle.None;
        //            richTextBox.BackColor = form.BackColor;
        //            richTextBox.Location = new Point(20, 20);
        //            richTextBox.Size = new Size(440, 180);
        //            richTextBox.WordWrap = true;
        //            richTextBox.ScrollBars = RichTextBoxScrollBars.Vertical;

        //            // 使用黑体和宋体两种字体，黑体加粗用于提示文字
        //            richTextBox.Rtf = $@"{{\rtf1\ansi\deff0
        //{{\fonttbl
        //    {{\f0\fnil\fcharset134 宋体;}}
        //    {{\f1\fnil\fcharset134 黑体;}}
        //}}
        //{{\colortbl ;\red255\green0\blue0;}}
        //\b\f1 内部参考号：\b0\f0 {reference}\line
        //\b\f1 发件人：\b0\f0 {senderEmail}\line
        //\b\f1 邮件标题：\b0\f0 {subject}
        //}}";

        //            form.Controls.Add(richTextBox);

        //            Button btnYes = new Button();
        //            btnYes.Text = "是";
        //            btnYes.Location = new Point(150, 220);
        //            btnYes.DialogResult = DialogResult.Yes;
        //            form.Controls.Add(btnYes);

        //            Button btnNo = new Button();
        //            btnNo.Text = "否";
        //            btnNo.Location = new Point(260, 220);
        //            btnNo.DialogResult = DialogResult.No;
        //            form.Controls.Add(btnNo);

        //            form.AcceptButton = btnYes;
        //            form.CancelButton = btnNo;

        //            return form.ShowDialog();
        //        }
        #endregion

        #region 将选中的邮件转换为PNG图片（包含邮件头信息）
        public static async Task<string> ConvertSelectedMailToPngWithHeaderAsync()
        {
            Outlook.Application app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;

            if (selection.Count == 0 || !(selection[1] is Outlook.MailItem mailItem))
                throw new InvalidOperationException("请先选择一封邮件。");

            string subject = mailItem.Subject;
            string from = mailItem.Sender?.Address ?? "";
            string to = mailItem.To;
            string cc = mailItem.CC;
            string sent = mailItem.SentOn.ToString("yyyy-MM-dd HH:mm");
            string bodyHtml = mailItem.HTMLBody;

            string tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);

            // 处理内嵌图片 cid
            foreach (Outlook.Attachment attachment in mailItem.Attachments)
            {
                if (!string.IsNullOrEmpty(attachment.PropertyAccessor.GetProperty("http://schemas.microsoft.com/mapi/proptag/0x3712001F") as string))
                {
                    string cid = attachment.PropertyAccessor.GetProperty("http://schemas.microsoft.com/mapi/proptag/0x3712001F") as string;
                    string localPath = Path.Combine(tempDir, cid + Path.GetExtension(attachment.FileName));
                    attachment.SaveAsFile(localPath);
                    bodyHtml = bodyHtml.Replace($"cid:{cid}", $"file:///{localPath.Replace("\\", "/")}");
                }
            }

            // 包裹完整 HTML
            string fullHtml = $@"
    <html>
    <head><meta http-equiv='X-UA-Compatible' content='IE=11'></head>
    <body style='font-family:sans-serif; font-size:14px;'>
      <div>
        <strong>发件人：</strong> {from}<br>
        <strong>收件人：</strong> {to}<br>
        <strong>抄送：</strong> {cc}<br>
        <strong>主题：</strong> {subject}<br>
        <strong>时间：</strong> {sent}<br>
      </div>
      <hr>
      <div>{bodyHtml}</div>
    </body>
    </html>";

            string imagePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid() + ".png");

            TaskCompletionSource<bool> tcs = new TaskCompletionSource<bool>();

            var thread = new Thread(() =>
            {
                using (var browser = new WebBrowser())
                {
                    browser.ScrollBarsEnabled = false;
                    browser.ScriptErrorsSuppressed = true;
                    browser.Width = 800;
                    browser.Height = 100;

                    browser.DocumentCompleted += (s, e) =>
                    {
                        browser.Height = browser.Document.Body.ScrollRectangle.Height;

                        using (Bitmap bmp = new Bitmap(browser.Width, browser.Height))
                        {
                            browser.DrawToBitmap(bmp, new Rectangle(0, 0, browser.Width, browser.Height));
                            bmp.Save(imagePath, ImageFormat.Png);
                        }

                        tcs.SetResult(true);
                        Application.ExitThread(); // 仅关闭该线程的消息循环
                    };

                    browser.DocumentText = fullHtml;
                    Application.Run(); // 启动消息循环
                }
            });

            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();

            await tcs.Task;

            return imagePath;
        }



        #endregion

        #region 将选中的邮件转换为PNG图片（包含邮件头信息）异步方法

        public static async Task<string> ConvertSelectedMailToPngBase64Async()
        {
            Outlook.Application app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;

            if (selection.Count == 0 || !(selection[1] is Outlook.MailItem mailItem))
                throw new InvalidOperationException("请先选择一封邮件。");

            string subject = mailItem.Subject;
            string from = mailItem.Sender?.Address ?? "";
            string to = mailItem.To;
            string cc = mailItem.CC;
            string sent = mailItem.SentOn.ToString("yyyy-MM-dd HH:mm");
            string bodyHtml = mailItem.HTMLBody;

            // 创建临时目录保存内嵌图片
            string tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);

            // 替换内嵌图片 CID 引用为本地文件路径
            foreach (Outlook.Attachment attachment in mailItem.Attachments)
            {
                try
                {
                    string cid = attachment.PropertyAccessor.GetProperty("http://schemas.microsoft.com/mapi/proptag/0x3712001F") as string;
                    if (!string.IsNullOrEmpty(cid))
                    {
                        string localPath = Path.Combine(tempDir, cid + Path.GetExtension(attachment.FileName));
                        attachment.SaveAsFile(localPath);
                        bodyHtml = bodyHtml.Replace($"cid:{cid}", $"file:///{localPath.Replace("\\", "/")}");
                    }
                }
                catch { }
            }

            // 拼接完整 HTML 页面
            string fullHtml = $@"
        <html>
        <head>
            <meta http-equiv='X-UA-Compatible' content='IE=11'>
            <style>
                body {{ font-family: sans-serif; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div>
                <strong>发件人：</strong> {from}<br>
                <strong>收件人：</strong> {to}<br>
                <strong>抄送：</strong> {cc}<br>
                <strong>主题：</strong> {subject}<br>
                <strong>时间：</strong> {sent}<br>
            </div>
            <hr>
            <div>{bodyHtml}</div>
        </body>
        </html>";

            TaskCompletionSource<byte[]> tcs = new TaskCompletionSource<byte[]>();

            var thread = new Thread(() =>
            {
                using (var browser = new WebBrowser())
                {
                    browser.ScrollBarsEnabled = false;
                    browser.ScriptErrorsSuppressed = true;
                    browser.Width = 900;
                    browser.Height = 100;

                    browser.DocumentCompleted += (s, e) =>
                    {
                        try
                        {
                            browser.Height = browser.Document?.Body?.ScrollRectangle.Height ?? 1000;

                            using (Bitmap bmp = new Bitmap(browser.Width, browser.Height))
                            {
                                browser.DrawToBitmap(bmp, new Rectangle(0, 0, bmp.Width, bmp.Height));
                                using (MemoryStream ms = new MemoryStream())
                                {
                                    bmp.Save(ms, ImageFormat.Png);
                                    tcs.SetResult(ms.ToArray());
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                        finally
                        {
                            Application.ExitThread();
                        }
                    };

                    browser.DocumentText = fullHtml;
                    Application.Run();
                }
            });

            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();

            byte[] imageBytes = await tcs.Task;

            // 删除临时图片目录
            try { Directory.Delete(tempDir, true); } catch { }

            // 转换为 base64 字符串（无 data:image/png;base64 前缀）
            return Convert.ToBase64String(imageBytes);
        }

        #endregion

    }
}
