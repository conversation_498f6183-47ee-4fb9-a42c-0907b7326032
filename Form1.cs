﻿using Microsoft.Office.Interop.Outlook;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;

namespace webhook
{
    public partial class Form1 : Form
    {
        private static Form1 instance;
        public static Form1 GetInstance()
        {
            if (instance == null || instance.IsDisposed)
            {
                instance = new Form1();
            }
            return instance;
        }
        public Form1()
        {
            InitializeComponent();
            
            // 设置窗体显示在屏幕中央，确保可见
            this.StartPosition = FormStartPosition.CenterScreen;

            var app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;
            if (selection.Count > 0 && selection[1] is MailItem mail)
            {

                Match match = Regex.Match(mail.Subject, @"G(R)?\d{6}WN\d{1,2}");
                if (match.Value == "")
                {
                    TXT_BHAO.Text = Helper.Convvert_BH(DateTime.Now);
                    TXT_BHAO.ForeColor = Color.Blue;
                }
                else
                {
                    TXT_BHAO.Text = match.Value;
                    TXT_BHAO.ForeColor = Color.Red;
                }
                TXT_BHAO.Select(0, 0);
            }


                //自动生成编号
                //TXT_BHAO.Text = Helper.Convvert_BH(DateTime.Now);


            // 确保窗体在前台显示
            this.TopMost = true;
            
            // 确保窗体可见
            this.Visible = true;
            this.Show();
            this.BringToFront();
            this.Focus();
            
            // 设置窗体大小
            this.Width = 833;
            this.Height = 507;
        }
       
        // 获取Outlook主窗口位置的方法（保留但不使用）
        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
        
        [DllImport("user32.dll", SetLastError = true)]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
        
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        private void button1_Click(object sender, EventArgs e)
        {

          



        }
        
        static string FormatOutlookEmail(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return "";

            // 1. 替换 "\r\n" 为真正的换行符
            string output = input.Replace("\\r\\n", "\n");

            // 2. 移除 HTML 链接的格式 <https://...>
            output = Regex.Replace(output, @"<https?:\/\/[^>]+>", "");

            // 3. 移除 mailto 链接格式 <mailto:<EMAIL>>
            output = Regex.Replace(output, @"<mailto:[^>]+>", "");

            // 4. 移除冗余空行（超过2行的压缩为1行）
            output = Regex.Replace(output, @"\n{3,}", "\n\n");

            // 5. 修复一些邮件中常见问题，如： "cc" 独立一行
            output = Regex.Replace(output, @"(?<=\n)cc", "CC");

            // 6. 清除行首多余空格
            output = Regex.Replace(output, @"(?m)^[ \t]+", "");

            return output.Trim();
        }

        private void button2_Click(object sender, EventArgs e)
        {

        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            string ysms = "";
            string bhao, from, to, vol,price;
           // double price;
            string pattern = @"^(G|GR)\d{4,6}WN\d{1,2}$";
            string reg_tszf = @"[\\:?""<>|]";  // 注意：\ 和 " 都要转义
            string excel_name = "";
            ExcelHelper1 excel1;
            string template_base = @"E:\winnie\报价单\template\";
            string bjd_base = @"E:\winnie\报价单\";

            if (TXT_BHAO.Text.Trim() == "")
            {
                MessageBox.Show("请输入编号");
                return;
            } else
            {
                if (Regex.IsMatch(TXT_BHAO.Text.Trim(), pattern))
                {
                    bhao = TXT_BHAO.Text.Trim();
                }
                else
                {
                    MessageBox.Show("请输入正确的编号格式");
                    return;
                }
            }
            if (TXT_FROM.Text.Trim() == "")
            {
                MessageBox.Show("请输入POD或者提货地址。");
                return;
            }
            else
            {
                if (Regex.IsMatch(TXT_FROM.Text.Trim(), reg_tszf))
                {
                    MessageBox.Show("from中包含了特殊字符。不能为\\ / : * ? \" < > | ");
                    return;
                }
                else
                {
                    from = TXT_FROM.Text.Trim();
                }
                
            }
            if (TXT_TO.Text.Trim() == "")
            {
                MessageBox.Show("请输入派送地址邮编");
                return;
            }
            else
            {
                if (Regex.IsMatch(TXT_TO.Text.Trim(), reg_tszf))
                {
                    MessageBox.Show("to中包含了特殊字符。不能为\\ / : * ? \" < > | ");
                    return;
                }
                else
                {
                    to = TXT_TO.Text.Trim();
                }
               
            }
            if (TXT_VOL.Text.Trim() == "")
            {
                MessageBox.Show("请输入货量。");
                return;
            }
            else
            {
                if (Regex.IsMatch(TXT_VOL.Text.Trim(), reg_tszf))
                {
                    MessageBox.Show("VOL中包含了特殊字符。不能为\\ / : * ? \" < > | ");
                    return;
                }
                else
                {
                    vol = TXT_VOL.Text.Trim();
                }
               
            }
            if (TXT_PRICE.Text.Trim() == "")
            {
                MessageBox.Show("请输入价格");
                return;
            }
            else
            {
                //   bool isNumeric = double.TryParse(textBox1.Text, out double result);
                if (double.TryParse(TXT_PRICE.Text.Trim(), out double result))
                {

                    price = TXT_PRICE.Text.Trim();

                }
                else
                {
                    MessageBox.Show("请输入正确的价格！");
                    return;
                }
          
            }

            string dg = "_";

            //判断选择的是什么运输模式
            if (R_AIR.Checked)
            {
                ysms = "AIR";
            }
            else if (R_FCL.Checked)
            {
                ysms = "FCL";
            } else if (R_LCL.Checked)
            {
                ysms = "LCL";
            } else if (R_TL_FCL.Checked)
            {
                ysms = "RAIL_FCL";
            } else if (R_TL_LCL.Checked)
            {
                ysms = "RAIL_LCL";
            } 


            if (check_dg.Checked)
            {
                dg = "_DG_";
            }

            excel_name = bhao + "-" + ysms + "-" + from.Replace("/",",") + " to "+to + "-" + vol.Replace("*","x").Replace("/",",") + ".xlsx";

            //判断是否有相同的报价单或者编号。
            if (File.Exists(@"E:\winnie\报价单\" + excel_name))
            {
                MessageBox.Show("已存在此报价文件");
            }
            else
            {
                if (Directory.GetFiles(@"E:\winnie\报价单\").Any(file => Path.GetFileName(file).Contains(bhao)))
                {

                    MessageBox.Show("已存在此编号报价单");

                }
                else
                {

                    //excel1 = new ExcelHelper1(template_base + ysms+dg+"template.xlsx");
                    //excel1.SaveAs(@"E:\winnie\报价单\" + excel_name);

                    switch(ysms)
                    {
                        case "AIR":
                            excel1 = new ExcelHelper1(template_base + ysms  + "_template.xlsx");
                            excel1.SaveAs(bjd_base + excel_name);
                            excel1.WriteData(from,8,3);
                            excel1.WriteData(vol, 9, 3);
                            excel1.WriteData(to, 11, 3);
                            excel1.WriteData(price, 16, 3);

                            break;
                        case "FCL":
                            if (check_dg.Checked)
                            {
                                excel1 = new ExcelHelper1(template_base + ysms + dg + "template.xlsx");
                                excel1.SaveAs(bjd_base + excel_name);
                                excel1.WriteData(price, 22, 3);
                            }
                            else
                            {
                                excel1 = new ExcelHelper1(template_base + ysms + "_template.xlsx");
                                excel1.SaveAs(bjd_base + excel_name);
                                excel1.WriteData(price, 21, 3);
                            }                             
                            excel1.WriteData(from, 6, 3);
                            excel1.WriteData(vol, 7, 3);
                            excel1.WriteData(to, 9, 3);      
                            break;
                        case "LCL":
                            if (check_dg.Checked)
                            {
                                excel1 = new ExcelHelper1(template_base + ysms + dg + "template.xlsx");
                                excel1.SaveAs(bjd_base + excel_name);
                                excel1.WriteData(price, 22, 3);
                            }
                            else
                            {
                                excel1 = new ExcelHelper1(template_base + ysms + "_template.xlsx");
                                excel1.SaveAs(bjd_base + excel_name);
                                excel1.WriteData(price, 21, 3);
                            }
                            excel1.SaveAs(bjd_base + excel_name);
                            excel1.WriteData(price, 12, 3);
                            excel1.WriteData(from, 5, 3);
                            excel1.WriteData(vol, 6, 3);
                            excel1.WriteData(to, 7, 3);
                            break;
                        case "RAIL_FCL":
                            excel1 = new ExcelHelper1(template_base + ysms + "_template.xlsx");
                            excel1.SaveAs(bjd_base + excel_name);
                            excel1.WriteData(from, 5, 3);
                            excel1.WriteData(vol, 6, 3);
                            excel1.WriteData(to, 7, 3);
                            excel1.WriteData(price, 13, 3);
                            break;
                        case "RAIL_LCL":
                            excel1 = new ExcelHelper1(template_base + ysms + "_template.xlsx");
                            excel1.SaveAs(bjd_base + excel_name);
                            excel1.WriteData(from, 5, 3);
                            excel1.WriteData(vol, 6, 3);
                            excel1.WriteData(to, 7, 3);
                            excel1.WriteData(price, 12, 3);
                            
                            break;

                    }
                }
            }
        }

        private async void button2_Click_1(object sender, EventArgs e)
        {
            try
            {
                    // 原有的JSON数据发送
                    var jsonData = new
                    {
                        ref_number = TXT_BHAO.Text.Trim()
                    };

                    var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);

                    using (var client = new HttpClient())
                    {
                        client.DefaultRequestHeaders.Add("token", "sdyfangye");
                        client.DefaultRequestHeaders.Add("function", "get_sheet_info");

                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var response = client.PostAsync(Helper.WebHook_base + Helper.WebHook_func.get_sheet_info, content).Result;
                        string responseBody = await response.Content.ReadAsStringAsync();


                    if (responseBody.Contains("error"))
                    {

                        MessageBox.Show("google sheets无此参考号！");
                        return;

                    }else
                    {
                        var obj = JObject.Parse(responseBody);

                        if (obj["ref_number"].ToString() == "undefined")
                        {
                            MessageBox.Show("google sheets 无此参考号！");
                            return;
                        }
                        else
                        {
                            TXT_FROM.Text = obj["POD"].ToString();
                            TXT_TO.Text = obj["to"].ToString();
                            TXT_VOL.Text = obj["VOL"].ToString();
                            TXT_PRICE.Text = obj["price"].ToString();
                        }

                    }

                }



                       
               
            }
            catch (System.Exception ex)
            {
                MessageBox.Show("错误: " + ex.Message);
            }
        }

        private void check_dg_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void button3_Click(object sender, EventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            var selection = app.ActiveExplorer().Selection;
            TXT_BHAO.Text = "";
            TXT_FROM.Text = "";
            TXT_PRICE.Text = "";
            TXT_TO.Text = "";
            TXT_VOL.Text = "";
            if (selection.Count > 0 && selection[1] is MailItem mail)
            {

                Match match = Regex.Match(mail.Subject, @"G(R)?\d{6}WN\d{1,2}");
                if (match.Value == "")
                {
                    TXT_BHAO.Text = Helper.Convvert_BH(DateTime.Now);
                    TXT_BHAO.ForeColor = Color.Blue;
                }
                else
                {
                    TXT_BHAO.Text = match.Value;
                    TXT_BHAO.ForeColor = Color.Red;
                }
                TXT_BHAO.Select(0, 0);
            }

        }
    }
}
