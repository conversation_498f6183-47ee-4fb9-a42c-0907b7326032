﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Office.Tools.Ribbon;
using System.Drawing;
using Microsoft.Office.Core;

namespace webhook
{
    partial class Ribbon1 : Microsoft.Office.Tools.Ribbon.RibbonBase
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        public Ribbon1()
            : base(Globals.Factory.GetRibbonFactory())
        {
            InitializeComponent();
        }

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tab1 = this.Factory.CreateRibbonTab();
            this.group1 = this.Factory.CreateRibbonGroup();
            this.button1 = this.Factory.CreateRibbonButton();
            this.group4 = this.Factory.CreateRibbonGroup();
            this.eb_company = this.Factory.CreateRibbonEditBox();
            this.eb_from = this.Factory.CreateRibbonEditBox();
            this.eb_to = this.Factory.CreateRibbonEditBox();
            this.eb_vol = this.Factory.CreateRibbonEditBox();
            this.group5 = this.Factory.CreateRibbonGroup();
            this.button2 = this.Factory.CreateRibbonButton();
            this.eb_cbj = this.Factory.CreateRibbonEditBox();
            this.group2 = this.Factory.CreateRibbonGroup();
            this.button4 = this.Factory.CreateRibbonButton();
            this.separator1 = this.Factory.CreateRibbonSeparator();
            this.button5 = this.Factory.CreateRibbonButton();
            this.group3 = this.Factory.CreateRibbonGroup();
            this.button3 = this.Factory.CreateRibbonButton();
            this.tab1.SuspendLayout();
            this.group1.SuspendLayout();
            this.group4.SuspendLayout();
            this.group5.SuspendLayout();
            this.group2.SuspendLayout();
            this.group3.SuspendLayout();
            this.SuspendLayout();
            // 
            // tab1
            // 
            this.tab1.ControlId.ControlIdType = Microsoft.Office.Tools.Ribbon.RibbonControlIdType.Office;
            this.tab1.Groups.Add(this.group1);
            this.tab1.Groups.Add(this.group4);
            this.tab1.Groups.Add(this.group5);
            this.tab1.Groups.Add(this.group2);
            this.tab1.Groups.Add(this.group3);
            this.tab1.Label = "Webhook";
            this.tab1.Name = "tab1";
            // 
            // group1
            // 
            this.group1.Items.Add(this.button1);
            this.group1.Label = "保持报价到nocodb";
            this.group1.Name = "group1";
            // 
            // button1
            // 
            this.button1.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button1.Description = "将邮件发送到n8n平台";
            this.button1.Image = global::webhook.Properties.Resources.保存邮件;
            this.button1.Label = "保存邮件";
            this.button1.Name = "button1";
            this.button1.ShowImage = true;
            this.button1.SuperTip = "点击将当前选中的邮件发送到n8n平台进行处理";
            this.button1.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button1_ClickAsync);
            // 
            // group4
            // 
            this.group4.Items.Add(this.eb_company);
            this.group4.Items.Add(this.eb_from);
            this.group4.Items.Add(this.eb_to);
            this.group4.Items.Add(this.eb_vol);
            this.group4.Name = "group4";
            // 
            // eb_company
            // 
            this.eb_company.Label = "公司";
            this.eb_company.Name = "eb_company";
            this.eb_company.Text = null;
            // 
            // eb_from
            // 
            this.eb_from.Label = "FROM";
            this.eb_from.Name = "eb_from";
            this.eb_from.Text = null;
            // 
            // eb_to
            // 
            this.eb_to.Label = "  TO";
            this.eb_to.Name = "eb_to";
            this.eb_to.Text = null;
            // 
            // eb_vol
            // 
            this.eb_vol.Label = " VOL";
            this.eb_vol.Name = "eb_vol";
            this.eb_vol.Text = null;
            // 
            // group5
            // 
            this.group5.Items.Add(this.button2);
            this.group5.Items.Add(this.eb_cbj);
            this.group5.Label = "成本价登记";
            this.group5.Name = "group5";
            // 
            // button2
            // 
            this.button2.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button2.Image = global::webhook.Properties.Resources.upload;
            this.button2.Label = "上传数据库";
            this.button2.Name = "button2";
            this.button2.ShowImage = true;
            this.button2.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button2_Click_1);
            // 
            // eb_cbj
            // 
            this.eb_cbj.Label = "成本价";
            this.eb_cbj.Name = "eb_cbj";
            this.eb_cbj.Text = null;
            // 
            // group2
            // 
            this.group2.Items.Add(this.button4);
            this.group2.Items.Add(this.separator1);
            this.group2.Items.Add(this.button5);
            this.group2.Label = "操作";
            this.group2.Name = "group2";
            // 
            // button4
            // 
            this.button4.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button4.Description = "将邮件发送到n8n平台";
            this.button4.Image = global::webhook.Properties.Resources.update;
            this.button4.Label = "更新记录";
            this.button4.Name = "button4";
            this.button4.ShowImage = true;
            this.button4.SuperTip = "点击将当前选中的邮件发送到n8n平台进行处理";
            this.button4.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button4_Click);
            // 
            // separator1
            // 
            this.separator1.Name = "separator1";
            // 
            // button5
            // 
            this.button5.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button5.Image = global::webhook.Properties.Resources.n8n;
            this.button5.Label = "报价创建";
            this.button5.Name = "button5";
            this.button5.ShowImage = true;
            this.button5.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button5_Click);
            // 
            // group3
            // 
            this.group3.Items.Add(this.button3);
            this.group3.Label = "保存公司信息";
            this.group3.Name = "group3";
            // 
            // button3
            // 
            this.button3.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button3.Description = "将邮件发送到n8n平台";
            this.button3.Image = global::webhook.Properties.Resources.联系人;
            this.button3.Label = "保存联系人";
            this.button3.Name = "button3";
            this.button3.ShowImage = true;
            this.button3.SuperTip = "点击将当前选中的邮件发送到n8n平台进行处理";
            this.button3.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button3_Click);
            // 
            // Ribbon1
            // 
            this.Name = "Ribbon1";
            this.RibbonType = "Microsoft.Outlook.Explorer";
            this.Tabs.Add(this.tab1);
            this.Load += new Microsoft.Office.Tools.Ribbon.RibbonUIEventHandler(this.Ribbon1_Load);
            this.tab1.ResumeLayout(false);
            this.tab1.PerformLayout();
            this.group1.ResumeLayout(false);
            this.group1.PerformLayout();
            this.group4.ResumeLayout(false);
            this.group4.PerformLayout();
            this.group5.ResumeLayout(false);
            this.group5.PerformLayout();
            this.group2.ResumeLayout(false);
            this.group2.PerformLayout();
            this.group3.ResumeLayout(false);
            this.group3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        internal Microsoft.Office.Tools.Ribbon.RibbonTab tab1;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group1;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button1;
        internal RibbonGroup group3;
        internal RibbonButton button3;
        internal RibbonButton button4;
        internal RibbonButton button5;
        internal RibbonEditBox eb_company;
        internal RibbonGroup group2;
        internal RibbonSeparator separator1;
        internal RibbonGroup group4;
        internal RibbonEditBox eb_from;
        internal RibbonEditBox eb_to;
        internal RibbonEditBox eb_vol;
        internal RibbonGroup group5;
        internal RibbonEditBox eb_cbj;
        internal RibbonButton button2;
    }
}
