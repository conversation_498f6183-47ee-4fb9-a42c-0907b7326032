﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="webhook.vsto" version="*******" publicKeyToken="826c62a2b1ee5027" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="webhook" asmv2:product="webhook" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="webhook.dll.manifest" size="17592">
      <assemblyIdentity name="webhook.dll" version="*******" publicKeyToken="826c62a2b1ee5027" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>sZ9aDQjIjU1y+8dEUN+P6jfWsok12dFyAYmDhYsPmX0=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=MS-VLEHOMVHAVLV\Administrator" issuerKeyHash="9c9163046045408a6e1e6545492ac1232a2c0121" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>eItuLB1P9NowC6kWORtdpja7rh0cF7pwFBm2rYs2dJU=</DigestValue></Reference></SignedInfo><SignatureValue>UrT/rN9egpnDmGAcVlmK86xa939b+2RwyDKizNj6FlOUFUC0unY2Lkr4VSgGxnB2NCG69V/BgROt5vw3ZOL0omNHhxubh+R4o+JaLmVf/wyBv1lp1GcpD3Q1gift3FKLzVETqFmB744mlxzOaydpH5T/E7poqdJWBjuGm7QMfME=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>vKjR2TxOjluu5f60382El00bvbQU8c9soRWjFSK5HwbympjounUVhtNt3Vrwv+gOFd/C24/vAxPS1CrEIIV5rdB9/dBjCizSqbQE+CQFvN6FCMC3cOJpYIXlJTu/eNbmKzdwzEqwFF8K3sTVFry3n/p3LG++9f/KLlf0y7penXk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="9574368badb6191470ba171c1daebb36a65d1b3916a90b30daf44f1d2c6e8b78" Description="" Url=""><as:assemblyIdentity name="webhook.vsto" version="*******" publicKeyToken="826c62a2b1ee5027" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=MS-VLEHOMVHAVLV\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>YRfYcSlGRK2OfDa+H1qTE22kSpcZxNFLs7/Ja2IRrX0=</DigestValue></Reference></SignedInfo><SignatureValue>WHQtWlLx7qV2bAwjRgvMxP33mNyDFD6Pb8laL/BXhJdAO9L3NtsE99pZhivXaceKWY+E2HechU2RqSYGX4vwH/zpyOJ0St/e3Ayie/CYorAETF9BWqIFi0Fdn5132nAPjdaCxb2AuQ4Rsfyr0HZci6Kl4BR1CbEWRkqfPom1Bug=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>vKjR2TxOjluu5f60382El00bvbQU8c9soRWjFSK5HwbympjounUVhtNt3Vrwv+gOFd/C24/vAxPS1CrEIIV5rdB9/dBjCizSqbQE+CQFvN6FCMC3cOJpYIXlJTu/eNbmKzdwzEqwFF8K3sTVFry3n/p3LG++9f/KLlf0y7penXk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIICDTCCAXagAwIBAgIQF2LSlih+hpdF5xiXe5xpXDANBgkqhkiG9w0BAQsFADBFMUMwQQYDVQQDHjoATQBTAC0AVgBMAEUASABPAE0AVgBIAEEAVgBMAFYAXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMB4XDTI1MDUxOTE1MjU1MloXDTI2MDUxOTIxMjU1MlowRTFDMEEGA1UEAx46AE0AUwAtAFYATABFAEgATwBNAFYASABBAFYATABWAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAvKjR2TxOjluu5f60382El00bvbQU8c9soRWjFSK5HwbympjounUVhtNt3Vrwv+gOFd/C24/vAxPS1CrEIIV5rdB9/dBjCizSqbQE+CQFvN6FCMC3cOJpYIXlJTu/eNbmKzdwzEqwFF8K3sTVFry3n/p3LG++9f/KLlf0y7penXkCAwEAATANBgkqhkiG9w0BAQsFAAOBgQAOa72fQ3LzePd3T2r/OAVqZrojrp/RVas9aj8YGkwLW8nx5scSTg4nHb0EgE0zQnB89lQVMJVJZ7YR2sJCi3RFPMbrqT8r1p6pE3Z0DkJU+65beCvGYResbwpoPJIWbHuGLyN/+rrrHWVaDJZwtbpUN5bNbfSECgkKX96UJqIgog==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>