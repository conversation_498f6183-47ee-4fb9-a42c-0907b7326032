﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;
using System.IO;
using Newtonsoft.Json.Linq;




namespace webhook
{
    public class NocoDBClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _tableId;
        private readonly string _viewId;

        public NocoDBClient(string baseUrl, string xcToken, string tableId, string viewId)
        {
            _baseUrl = baseUrl.TrimEnd('/');
            _tableId = tableId;
            _viewId = viewId;

            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("xc-token", xcToken);
        }

        // 获取表记录列表
        public async Task<string> ListTableRecords(int limit = 25, int offset = 0)
        {
            var url = $"{_baseUrl}/api/v2/tables/{_tableId}/records";
            if (!string.IsNullOrEmpty(_viewId))
            {
                url += $"?viewId={_viewId}&limit={limit}&offset={offset}";
            }

            var response = await _httpClient.GetAsync(url);
            return await HandleResponse(response);
        }

        // 创建表记录
        public async Task<string> CreateTableRecord(object data)
        {
            var url = $"{_baseUrl}/api/v2/tables/{_tableId}/records";
            var content = new StringContent(
                JsonConvert.SerializeObject(data),
                Encoding.UTF8,
                "application/json");

            var response = await _httpClient.PostAsync(url, content);
            return await HandleResponse(response);
        }

        // 更新表记录
        public async Task<string> UpdateTableRecord(string rowId, object data)
        {
            var url = $"{_baseUrl}/api/v2/tables/{_tableId}/records/{rowId}";
            var content = new StringContent(
                JsonConvert.SerializeObject(data),
                Encoding.UTF8,
                "application/json");

            var request = new HttpRequestMessage(new HttpMethod("PATCH"), url)
            {
                Content = content
            };

            var response = await _httpClient.SendAsync(request);
            return await HandleResponse(response);
        }

        // 删除表记录
        public async Task<string> DeleteTableRecord(string rowId)
        {
            var url = $"{_baseUrl}/api/v2/tables/{_tableId}/records/{rowId}";
            var response = await _httpClient.DeleteAsync(url);
            return await HandleResponse(response);
        }

        // 上传文件
        public async Task<JObject> UploadFile(string filePath)
        {
            using (var multipartContent = new MultipartFormDataContent())
            using (var fileStream = File.OpenRead(filePath))
            using (var fileContent = new StreamContent(fileStream))
            {
                // 添加文件到表单
                multipartContent.Add(fileContent, "file", Path.GetFileName(filePath));

                // 发送请求
                var response = await _httpClient.PostAsync(
                    $"{_baseUrl}/api/v2/storage/upload?path=somePath",
                    multipartContent
                );

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseContent = await response.Content.ReadAsStringAsync();
                return JObject.Parse(responseContent);
            }
        }

        // 创建带附件的记录
        public async Task<string> CreateRecordWithAttachment(string title, string filePath)
        {
            // 上传文件
            var fileResponse = await UploadFile(filePath);

            // 准备记录数据
            var row = new
            {
                Title = title,
                Attachment = fileResponse
            };

            // 创建记录
            return await CreateTableRecord(row);
        }

        // 处理 API 响应
        private async Task<string> HandleResponse(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"API 请求失败: {response.StatusCode}, {content}");
            }

            return content;
        }
    }
}


